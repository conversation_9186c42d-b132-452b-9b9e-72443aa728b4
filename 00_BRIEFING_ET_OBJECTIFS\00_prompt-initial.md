Briefing Initial de la Mission

## 1. Objectif Principal

En tant qu'agent de développement IA, ton objectif premier est de m'assister dans la création d'un **référentiel d'instructions complet** pour un agent de développement IA. Ce référentiel doit être une arborescence de dossiers contenant **exclusivement des fichiers d'instructions au format Markdown (.md)**.

## 2. Rôle des Agents de Codage

**Chers agents de codage,**

Vous êtes chargés de suivre les directives de ce framework pour construire et maintenir nos applications. Il est crucial de comprendre que ce dossier (`/MON_PROJET`) ne contient pas de code applicatif, mais les **instructions détaillées** que vous devrez appliquer lors de vos tâches de développement.

**Sources de Connaissance :**
*   Votre source de vérité principale est le document "Architecture de Projet & Guide pour Agent IA (v3)".
*   Utilisez également la documentation officielle d'Augment Code pour des détails techniques précis. Augment est conçu pour vous aider à comprendre, déboguer et développer plus rapidement grâce à ses fonctionnalités de chat, next edit et complétions de code.

**Recherche de Bibliothèques et Solutions (MCP) :**
*   **Considérez tous les serveurs MCP (Master Control Program) pertinents** comme des sources inestimables de solutions et de bibliothèques.
*   **Context7** est un exemple clé. Ce serveur MCP vous fournit de la documentation de code à jour et spécifique à la version directement dans votre LLM [2, 3]. Cela vous permet d'obtenir des exemples de code et de la documentation actualisés directement de la source, sans dépendre d'informations obsolètes ou génériques.
*   Si vous avez la capacité via un accès web, **recherchez activement d'autres serveurs MCP** spécialisés dans les technologies de notre stack.
*   Vous êtes autorisés à **proposer l'installation de bibliothèques fiables** que vous découvrez via ces MCP, après mon approbation.

**Qualité des Instructions :**
Les instructions que vous créerez doivent être non seulement conformes à ma vision, mais aussi techniquement précises et compréhensibles par tout agent de code.
