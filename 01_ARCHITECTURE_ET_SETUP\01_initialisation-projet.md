**Objectif :** Ce document guide l'agent de développement IA pour l'initialisation d'un nouveau projet ou l'évaluation d'une codebase existante, en respectant la structure définie.

**Directives pour l'agent de codage :**

1.  **Synthèse des sources :** Référez-vous à la "Architecture de Projet & Guide pour Agent IA (v3)" comme source principale [1], mais intégrez activement la documentation officielle d'Augment Code et toute autre source pertinente que vous avez à disposition pour les détails techniques. Votre rôle est de synthétiser ces informations pour produire des instructions précises et compréhensibles.
2.  **Rôle des serveurs MCP (Master Control Program) :**
    *   Considérez tous les serveurs MCP pertinents comme des sources de solutions et de bibliothèques.
    *   **Context7 est un exemple crucial de serveur MCP**. Il est conçu pour fournir une documentation de code à jour et spécifique à la version directement dans votre LLM, évitant les informations obsolètes ou les APIs hallucinées [5, 6]. Lorsque vous êtes invité à générer du code ou à proposer des solutions techniques, utilisez la directive `use context7` dans votre prompt si vous opérez dans un client MCP compatible (comme Cursor, Windsurf, Claude Desktop, Augment Code, JetBrains AI Assistant, VS Code) pour obtenir des réponses fonctionnelles et sans hallucinations.
    *   Si vous avez la capacité via un accès web, **recherchez activement d'autres serveurs MCP** spécialisés dans les technologies de notre stack.
    *   Vous êtes autorisé à proposer l'installation de bibliothèques fiables découvertes via ces MCP, après mon approbation .
3.  **Nature du référentiel :** Rappelez-vous que ce dossier (`/MON_PROJET`) est un **manuel d'instructions 100% Markdown** et ne doit contenir **aucun fichier de code applicatif** (.tsx, .ts, .css, etc.).
4.  **Interprétation de l'arborescence cible :**
    *   **Si la tâche implique la création d'un projet de zéro :** Vous devrez créer la structure d'arborescence de dossiers de code spécifiée (ex: `/components`, `/pages`) dans l'emplacement dédié au projet applicatif.
    *   **Si la tâche implique l'importation ou l'analyse d'un projet existant :** Vous ne devez **RIEN** créer. Votre rôle est de scanner la structure existante du projet importé et de la comparer à l'arborescence cible définie. Vous me signalerez ensuite les différences, les manques ou les non-conformités, en attendant mes instructions spécifiques.

---