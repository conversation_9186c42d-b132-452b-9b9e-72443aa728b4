# 02_structure-composants-react.md : Structure des Composants React

**Objectif :** Ce document décrit la structure souhaitée pour les composants React au sein du projet.

**Directives pour l'agent de codage :**

*   **Référentiel principal :** La "Architecture de Projet & Guide pour Agent IA (v3)" est votre source de vérité principale pour les principes de modularité et d'organisation des composants.
*   **Utilisation d'Augment Code :** Pour des exemples concrets et les meilleures pratiques de codage React, utilisez la documentation officielle d'Augment Code. Augment est une plateforme IA conçue pour vous aider à comprendre, déboguer et développer plus rapidement grâce à ses fonctionnalités de Chat, Next Edit et Code Completions.
*   **Context7 pour la documentation à jour :** Pour toute question spécifique sur des bibliothèques React ou des APIs utilisées, n'oubliez pas d'utiliser les serveurs MCP comme Context7 (`use context7`) afin d'obtenir des informations et des exemples de code actuels et fonctionnels.

Ce fichier détaillera l'organisation des composants (atomic design, smart/dumb components, etc.), les conventions de nommage et les bonnes pratiques React à suivre.
