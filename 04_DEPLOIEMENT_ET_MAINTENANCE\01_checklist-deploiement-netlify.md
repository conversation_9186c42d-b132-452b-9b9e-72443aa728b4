# Stratégie de Déploiement

Ce document établit les principes pour la préparation et l'exécution du déploiement des applications développées par l'agent de développement IA.

# **Checklist de Déploiement : Netlify**

Ce document fournit les étapes précises pour déployer une application sur Netlify.

### **Phase 1 : Pré-requis**

* \[ \] Le code a été poussé sur un repository GitHub (ou GitLab/Bitbucket).  
* \[ \] La branche de déploiement (généralement main ou master) est propre, stable et testée.  
* \[ \] Toutes les clés d'API et secrets sont dans un fichier .env.local qui est bien présent dans le .gitignore.

### **Phase 2 : Configuration du Site sur Netlify**

1. **Créer un nouveau site :**  
   * \[ \] Connecte-toi à Netlify.  
   * \[ \] Clique sur "Add new site" \-\> "Import an existing project".  
   * \[ \] Choisis ton fournisseur Git (ex: GitHub) et autorise l'accès.  
   * \[ \] Sélectionne le repository de ton projet.  
2. **Paramètres de Build :**  
   * \[ \] **Branche de déploiement :** Assure-toi que la branche sélectionnée est main (ou ta branche de production).  
   * \[ \] **Commande de build :** vite build (ou npm run build si configuré ainsi dans package.json).  
   * \[ \] **Dossier de publication :** dist. Vite génère le build dans ce dossier par défaut.

### **Phase 3 : Variables d'Environnement**

C'est l'étape la plus critique.

1. **Accéder aux variables :**  
   * \[ \] Va dans "Site settings" \-\> "Build & deploy" \-\> "Environment".  
   * \[ \] Clique sur "Edit variables".  
2. **Ajouter les variables :**  
   * \[ \] Ajoute **UNE PAR UNE** toutes les variables de ton fichier .env.local.  
   * **IMPORTANT :** Sur Netlify, tu n'as PAS besoin du préfixe VITE\_ dans le nom de la variable. Le code import.meta.env.VITE\_FIREBASE\_API\_KEY fonctionnera quand même, car Vite gère cela au moment du build.  
   * **Exemple :**  
     * Nom de la variable : VITE\_FIREBASE\_API\_KEY  
     * Valeur : AIza...  
   * \[ \] Répète l'opération pour toutes les clés (Auth Domain, Project ID, etc.).

### **Phase 4 : Déploiement**

* \[ \] Clique sur "Deploy site" (ou "Trigger deploy" si le site est déjà créé).  
* \[ \] Surveille le log de déploiement en temps réel pour toute erreur. Les erreurs de build sont souvent liées à des dépendances manquantes ou des variables d'environnement incorrectes.  
* \[ \] Une fois le déploiement marqué comme "Published", visite l'URL fournie par Netlify pour tester l'application en production.

## 1. Assurance Qualité Pré-Déploiement

*   **Validation finale du code** : Avant tout déploiement, l'agent doit s'assurer que le code a passé toutes les étapes de la checklist qualité et des scénarios de test manuel.
*   **Vérification de la précision technique** : L'agent doit systématiquement valider la qualité et l'exactitude du code produit, car les réponses générées par l'IA peuvent contenir des erreurs.
*   **Utilisation des serveurs MCP pour les outils de déploiement** : L'agent doit considérer les serveurs MCP pertinents, tels que Context7, comme des sources primaires pour obtenir de la documentation à jour sur les outils et les plateformes de déploiement (par exemple, des scripts, des configurations d'intégration continue). Context7 est spécifiquement conçu pour fournir une documentation de code à jour et spécifique à la version pour les LLM et les éditeurs de code IA, aidant à éviter les exemples de code obsolètes ou les API inexistantes.

## 2. Automatisation et Scripting

*   **Proposition de scripts de déploiement** : Si nécessaire, l'agent peut proposer des scripts d'automatisation pour le déploiement, en s'appuyant sur les pratiques de qualité du code.
*   **Débogage des problèmes de déploiement** : En cas d'échec du déploiement, l'agent doit utiliser ses capacités de compréhension du code et de débogage (via Augment) pour identifier et résoudre les problèmes liés aux scripts, aux configurations ou à l'environnement cible.

## 3. Documentation du Déploiement

*   **Consignation des procédures** : L'agent doit s'assurer que les étapes de déploiement et les configurations spécifiques à l'environnement sont documentées de manière claire et précise.
*   **Utilisation de Context7 pour la documentation technique** : Pour toute question de code, de configuration ou de documentation relative aux outils de déploiement, l'agent est encouragé à invoquer Context7 pour obtenir des informations fiables et à jour, contribuant ainsi à la précision technique et à la qualité de la documentation.

## 4. Test de Déploiement

*   **Exécution de tests de déploiement** : Avant chaque déploiement, l'agent doit exécuter des tests de déploiement pour s'assurer que l'application fonctionne correctement dans l'environnement cible.
*   **Documentation des tests de déploiement** : Les résultats des tests de déploiement doivent être documentés, notamment les erreurs détectées et les modifications apportées pour corriger les problèmes.
*   **Rapport de dépannage** : Si un problème survient durant le déploiement, l'agent doit fournir un rapport détaillé pour aider les équipes de support à identifier et à résoudre le problème.

## 5. Documentation de la Qualité du Code

*   **Documentation des tests de qualité** : Les résultats des tests de qualité doivent être documentés, notamment les erreurs détectées et les modifications apportées pour corriger les problèmes.
*   **Rapport de dépannage** : Si un problème survient durant la phase de qualité, l'agent doit fournir un rapport détaillé pour aider les équipes de support à identifier et à résoudre le problème.
