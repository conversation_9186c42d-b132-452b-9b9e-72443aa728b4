# **Checklist de Déploiement : Firebase App Hosting**

Ce document fournit les étapes pour déployer une application web sur Firebase App Hosting, idéalement via une intégration continue avec GitHub.

### **Phase 1 : Pré-requis**

* \[ \] Le code a été poussé sur un repository GitHub.  
* \[ \] Tu as un projet Firebase créé sur [console.firebase.google.com](https://console.firebase.google.com).  
* \[ \] Tu as installé Firebase CLI sur ta machine : npm install \-g firebase-tools.

### **Phase 2 : Configuration Initiale (Une seule fois par projet)**

1. **Connexion et Initialisation :**  
   * \[ \] Ouvre un terminal à la racine de ton projet.  
   * \[ \] Connecte-toi à Firebase : firebase login.  
   * \[ \] Initialise le projet : firebase init.  
   * \[ \] Choisis "Hosting: Configure and deploy Firebase Hosting sites".  
   * \[ \] Sélectionne "Use an existing project" et choisis ton projet dans la liste.  
2. **Configuration de l'Hébergement :**  
   * \[ \] **Quel est votre dossier public ?** Tape dist. C'est le dossier où Vite build le projet.  
   * \[ \] **Configurer comme une single-page app (rewrite all urls to /index.html) ?** Réponds Yes (ou y). C'est crucial pour React.  
   * \[ \] **Set up automatic builds and deploys with GitHub?** Réponds Yes (ou y).  
   * \[ \] Suis les instructions pour te connecter à GitHub et sélectionner ton repository.  
   * \[ \] **What script should be run before every deploy?** Tape npm ci && npm run build.  
   * \[ \] Firebase va créer un fichier de workflow GitHub Actions (.github/workflows/firebase-hosting-pull-request.yml et/ou firebase-hosting-merge.yml).

### **Phase 3 : Déploiement**

1. **Commit les changements :**  
   * \[ \] Ajoute les nouveaux fichiers créés par Firebase (.firebaserc, firebase.json, et le dossier .github) à Git.  
   * \[ \] Fais un commit et push sur ta branche main.  
2. **Déploiement Automatique :**  
   * \[ \] En poussant sur la branche main, tu vas déclencher l'action GitHub configurée.  
   * \[ \] Va dans l'onglet "Actions" de ton repository GitHub pour suivre le déploiement en temps réel.  
   * \[ \] Une fois l'action terminée avec succès, ton site est déployé sur l'URL de ton projet Firebase (ex: mon-projet.web.app).

**Note sur les variables d'environnement :** Pour que le build sur GitHub Actions fonctionne, tu devras ajouter tes clés VITE\_FIREBASE\_API\_KEY, etc., comme "Secrets" dans les paramètres de ton repository GitHub (Settings \> Secrets and variables \> Actions). L'action GitHub les utilisera alors pendant le build.



# Surveillance et Maintenance

Ce document décrit les directives pour l'agent de développement IA en matière de surveillance, de mise à jour et d'amélioration continue des applications après leur déploiement.

## 1. Compréhension du Codebase pour la Maintenance

*   **Analyse du code existant** : Augment, en tant que plateforme d'IA pour développeurs, aide à comprendre le code. L'agent doit tirer parti de cette capacité pour analyser le codebase existant lors de l'identification de la cause des problèmes ou de l'implémentation de nouvelles fonctionnalités ou mises à jour.
*   **Débogage des problèmes post-déploiement** : Augment est conçu pour aider à déboguer les problèmes, ce qui est crucial pour maintenir la qualité des applications en production. L'agent doit utiliser activement cette fonctionnalité pour diagnostiquer et résoudre les incidents.

## 2. Mises à Jour et Évolutions

*   **Recherche de bibliothèques et de versions à jour** : Pour la maintenance évolutive ou corrective, l'agent est encouragé à rechercher activement les serveurs MCP, y compris Context7, pour des versions à jour de bibliothèques et des exemples de code pertinents. L'intégration de Context7 permet d'éviter les problèmes courants tels que les exemples de code obsolètes et les API "hallucinées".
*   **Proposition d'installation de bibliothèques fiables** : L'agent est autorisé à proposer l'installation de bibliothèques fiables découvertes sur ces serveurs pour améliorer ou maintenir l'application, après approbation.

## 3. Qualité et Précision Technique Continues

*   **Validation systématique des interventions** : Toute modification apportée pendant la phase de maintenance doit être soumise aux mêmes exigences de précision technique et de validation que le code initial. L'agent doit systématiquement valider la qualité et l'exactitude des informations et du code produits.
*   **Reporting des problèmes persistants ou nouveaux** : Si des problèmes surviennent ou persistent, l'agent doit suivre la procédure de rapport de bugs et fournir un feedback détaillé pour contribuer à l'amélioration continue des outils d'IA et de l'application elle-même.

## 4. Déploiement et Maintenance

*   **Surveillance continue des applications** : L'agent est responsable de la surveillance continue des applications déployées pour détecter et résoudre rapidement les problèmes.
*   **Documentation des mises à jour et des changements** : Toutes les mises à jour et les changements apportés au codebase doivent être documentés clairement pour une future maintenance et compréhension.