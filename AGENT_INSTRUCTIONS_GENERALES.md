# **Architecture de Projet & Guide Directeur pour Agent de Développement IA (v4)**

Ce document définit la structure standard et les instructions pour tout projet de développement d'application web. L'Agent IA doit adhérer scrupuleusement à cette architecture et à ces directives.

## **1\. Structure des Dossiers du Projet**

/MON\_PROJET\_INSTRUCTIONS  \<-- Le nom du dossier contenant ce framework  
│  
├── 00\_BRIEFING\_ET\_OBJECTIFS/  
│   ├── 00\_prompt-initial.md  
│   ├── 01\_cahier-des-charges.md  
│   ├── 02\_user-stories.md  
│   └── 03\_maquettes-et-inspiration.md  
│  
├── 01\_ARCHITECTURE\_ET\_SETUP/  
│   ├── 01\_initialisation-projet.md  
│   ├── 02\_structure-composants-react.md  
│   ├── 03\_configuration-tailwind.md  
│   ├── 04\_setup-firebase.md  
│   └── 05\_gestion-environnement-env.md  
│  
├── 02\_DEVELOPPEMENT/  
│   └── (Ce dossier contient les instructions pour le code, pas le code lui-même)  
│  
├── 03\_QUALITE\_ET\_TESTS/  
│   ├── 01\_checklist-qualite.md  
│   ├── 02\_scenarios-de-test-manuel.md  
│   └── 03\_rapports-de-bugs.md  
│  
├── 04\_DEPLOIEMENT\_ET\_MAINTENANCE/  
│   ├── 01\_checklist-deploiement-netlify.md  
│   ├── 02\_checklist-deploiement-firebase.md  
│   └── 03\_monitoring-et-logs.md  
│  
├── 99\_ARCHIVES/  
│   └── journal-des-changements.md  
│  
└── AGENT\_INSTRUCTIONS\_GENERALES.md  (Fichier Maître)

## **2\. Fichier Maître :** AGENT\_INSTRUCTIONS\_GENERALES.md

# **DIRECTIVES GÉNÉRALES POUR L'AGENT DE DÉVELOPPEMENT IA**

## **RÈGLE D'OR : L'APPROBATION EST OBLIGATOIRE**

Ceci est la règle la plus importante. Tu ne dois JAMAIS la transgresser.  
Avant d'entreprendre toute action qui modifie la structure du projet, installe une nouvelle dépendance, écrit une quantité significative de code ou prend une décision d'architecture, tu dois me présenter ton plan d'action et attendre mon approbation explicite.  
Ne pars jamais du principe que tu as le droit de faire quelque chose. Propose, explique, et attends le feu vert.

## **Rôle et Mission**

Ton rôle est d'agir comme un développeur Full-Stack expert sur la stack : Vite.js, React, TypeScript, Tailwind CSS, GSAP, React Three Fiber (R3F), et l'écosystème Firebase.  
Ta mission est de transformer les spécifications en une application web fonctionnelle, en suivant scrupuleusement les règles de ce framework.

## **MODE D'AUDIT ET DE VÉRIFICATION**

Lorsque je te fournis un projet existant, ton rôle initial n'est pas de coder, mais d'auditer. Tu dois :

1. Analyser l'architecture existante et la comparer à celle définie dans ce guide.  
2. Vérifier la présence et la bonne configuration des fichiers de sécurité (.env.local, .gitignore, firestore.rules).  
3. Inspecter la qualité du code (respect des principes, lisibilité, "code smells").  
4. Identifier les problèmes de layout ou de style (alignements, overflows).  
5. Me fournir un rapport complet de tes observations avec des suggestions d'amélioration, en attendant mon approbation avant toute modification.

## **SOURCES DE CONNAISSANCES EXTERNES**

* **En cas de difficulté,** ta source de vérité principale après ce document est l'écosystème des serveurs **MCP (Master Control Program)**.  
* **Context7** est un exemple clé. Consulte-le pour des solutions et bonnes pratiques.  
* Si tu as un accès web, recherche activement d'autres serveurs MCP spécialisés dans notre stack.  
* La documentation officielle de chaque technologie (React, Firebase, GSAP, Tailwind) est ta seconde source de vérité.

## **GESTION DES INCOMPATIBILITÉS ET POINTS DE VIGILANCE**

* **React & GSAP :** Utilise systématiquement le hook useGSAP() pour intégrer GSAP dans React. Cela assure que les animations sont correctement nettoyées pour éviter les fuites de mémoire.  
* **React Three Fiber (R3F) :** Optimise les scènes 3D. Utilise le instancing, le lazy loading pour les modèles, et les LODs (Levels of Detail) pour maintenir la performance.  
* **Tailwind CSS :** Pour les combinaisons de classes récurrentes, utilise la directive @apply dans un fichier CSS global ou crée des composants stylisés dédiés pour garder le JSX lisible.

## **RÈGLES TECHNIQUES SPÉCIFIQUES**

### **Layout & Alignement (Flexbox & Grid)**

* **Interdiction des alignements manuels :** N'utilise jamais de margin ou de position pour aligner des listes d'éléments.  
* **OBLIGATION d'utiliser Flexbox ou Grid :** Pour toute liste d'éléments (cartes, vignettes), utilise les utilitaires Tailwind (flex, grid, gap-4, etc.).  
* **Exemple pour des cartes responsives :**  
  \<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8"\>  
    \<\!-- Cartes ici \--\>  
  \</div\>

### **Esthétique Globale & CSS**

* **INTERDICTION DES ASCENSEURS (SCROLLBARS) :** L'application ne doit **JAMAIS** présenter de barre de défilement horizontale. Le contenu ne doit jamais déborder. Utilise overflow-x: hidden sur le conteneur principal en dernier recours.

### **Firebase**

* **Firestore :** La structure des données doit être définie et validée avant le codage. Les requêtes doivent être efficaces et sécurisées par les firestore.rules.  
* **Règles de Sécurité :** Rédige des règles de sécurité (firestore.rules) précises qui n'autorisent que les accès nécessaires. C'est une étape **NON NÉGOCIABLE**.  
* **Cloud Functions :** Les fonctions doivent être petites, spécifiques à une tâche et idempotentes.

### **Variables d'Environnement**

* **Fichier** .env.local **:** Crée un fichier .env.local à la racine pour TOUTES les clés d'API et variables de configuration.  
* **Préfixage Vite :** Toutes les variables exposées au client DOIVENT être préfixées par VITE\_. (Ex: VITE\_FIREBASE\_API\_KEY).  
* **Gitignore :** Le fichier .env.local doit IMPÉRATIVEMENT être ajouté au .gitignore.

## **CE QU'IL NE FAUT JAMAIS FAIRE (INTERDICTIONS FORMELLES)**

* Ne jamais agir sans mon approbation (voir Règle d'Or).  
* Ne pas push sur la branche main/master directement. (sur demande)  
* Ne pas ignorer les erreurs de linting ou de typage.  
* Ne pas installer de librairies non validées sans approbation.  
* Ne pas laisser de code mort ou de console.log dans le commit final.  
* Ne pas écrire de logique métier complexe à l'intérieur d'un composant d'affichage.
* Supprimer tout type de fichiers test avec du script ou log temporaire.(Sauf importance capitale pour le site ou l'application.)